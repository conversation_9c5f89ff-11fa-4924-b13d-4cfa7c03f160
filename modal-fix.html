<!DOCTYPE html>
<html>
<head>
    <title>Modal Fix Test</title>
    <style>
        /* Modal Styles */
        #hcmModal {
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(0, 0, 0, 0.8) !important;
            z-index: 99999 !important;
            justify-content: center !important;
            align-items: center !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
        
        .modal-content {
            background: linear-gradient(135deg, #2a3353 0%, #1e2746 100%) !important;
            border-radius: 12px !important;
            width: 90% !important;
            max-width: 500px !important;
            padding: 25px 20px !important;
            color: #fff !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }
        
        .modal-message {
            display: none !important;
        }
        
        .modal-message.active {
            display: block !important;
        }
        
        .modal-btn {
            background: linear-gradient(135deg, #00e1fd 0%, #0099cc 100%) !important;
            color: white !important;
            border: none !important;
            padding: 12px 24px !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            margin-top: 15px !important;
        }
        
        .modal-navigation {
            text-align: center !important;
            margin-top: 20px !important;
        }
    </style>
</head>
<body>
    <!-- Modal de información del módulo logístico -->
    <div id="hcmModal">
        <div class="modal-content">
            <!-- Mensaje del Piloto -->
            <div class="modal-message active" id="pilotMessage">
                <h3>🎯 Nueva Interfaz del Módulo Logístico</h3>
                <p>Estimado colaborador, nos complace informarte que ha sido liberada <strong>una nueva interfaz del módulo Logístico</strong>.</p>
                
                <h4>🎯 Objetivos del Piloto:</h4>
                <ul>
                    <li>Evaluar la usabilidad de la nueva interfaz</li>
                    <li>Identificar oportunidades de mejora</li>
                    <li>Optimizar los procesos logísticos</li>
                    <li>Recopilar feedback valioso para el desarrollo final</li>
                </ul>
                
                <p><strong>Tu participación es fundamental</strong> para el éxito de este proyecto. Valoramos enormemente tu experiencia y esperamos contar con tus comentarios, sugerencias y observaciones.</p>
                
                <a href="mod_logistica.php" style="
                    display: inline-block;
                    background: #00e1fd;
                    color: white;
                    text-decoration: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    margin: 15px 0;
                ">🔗 Acceder al Nuevo Módulo Logístico</a>
                
                <p style="margin-top: 15px; font-size: 0.9rem; color: rgba(255, 255, 255, 0.8);">
                    <strong>Nota:</strong> Puedes acceder al nuevo módulo en cualquier momento desde el botón de logística en el menú inferior.
                </p>
            </div>
            
            <!-- Navegación del modal -->
            <div class="modal-navigation">
                <button class="modal-btn" id="nextBtn">Entendido</button>
            </div>
        </div>
    </div>
    
    <script>
        console.log('🚀 Modal Fix Test - Iniciando...');
        
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('hcmModal');
            const nextBtn = document.getElementById('nextBtn');
            
            console.log('Modal encontrado:', !!modal);
            console.log('Botón encontrado:', !!nextBtn);
            
            if (nextBtn) {
                nextBtn.addEventListener('click', function() {
                    console.log('Cerrando modal');
                    if (modal) {
                        modal.style.display = 'none';
                    }
                });
            }
            
            // Cerrar con Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && modal) {
                    modal.style.display = 'none';
                }
            });
            
            console.log('✅ Modal Fix Test - Configurado');
        });
    </script>
</body>
</html>

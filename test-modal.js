// Script de prueba para verificar y mostrar el modal
console.log('🔍 SCRIPT DE PRUEBA - Verificando modal...');

// Verificar si el modal existe
const modal = document.getElementById('hcmModal');
console.log('Modal hcmModal encontrado:', !!modal);

if (modal) {
    console.log('✅ Modal existe, forzando visualización...');
    
    // Forzar estilos para mostrar el modal
    modal.style.display = 'flex';
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100%';
    modal.style.height = '100%';
    modal.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    modal.style.zIndex = '99999';
    modal.style.justifyContent = 'center';
    modal.style.alignItems = 'center';
    modal.style.opacity = '1';
    modal.style.visibility = 'visible';
    
    // Verificar contenido del modal
    const modalContent = modal.querySelector('.modal-content');
    console.log('Modal content encontrado:', !!modalContent);
    
    if (modalContent) {
        modalContent.style.display = 'block';
        modalContent.style.opacity = '1';
        modalContent.style.visibility = 'visible';
        modalContent.style.background = '#1e2746';
        modalContent.style.padding = '20px';
        modalContent.style.borderRadius = '12px';
        modalContent.style.color = 'white';
        modalContent.style.maxWidth = '500px';
        modalContent.style.width = '90%';
    }
    
    console.log('✅ Modal forzado a mostrarse');
} else {
    console.log('❌ Modal no existe en el DOM');
    
    // Crear modal dinámicamente para prueba
    console.log('🔧 Creando modal de prueba...');
    
    const testModal = document.createElement('div');
    testModal.id = 'testModal';
    testModal.innerHTML = `
        <div style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 99999;
            display: flex;
            justify-content: center;
            align-items: center;
        ">
            <div style="
                background: #1e2746;
                padding: 30px;
                border-radius: 12px;
                color: white;
                max-width: 500px;
                width: 90%;
                text-align: center;
            ">
                <h3>🔧 Modal de Prueba</h3>
                <p>Este es un modal de prueba creado dinámicamente para verificar que el sistema funciona.</p>
                <button onclick="document.getElementById('testModal').remove()" style="
                    background: #00e1fd;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    cursor: pointer;
                    margin-top: 15px;
                ">Cerrar</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(testModal);
    console.log('✅ Modal de prueba creado y mostrado');
}

// Verificar otros elementos relacionados
console.log('Verificando otros elementos...');
console.log('maintenanceMessage:', !!document.getElementById('maintenanceMessage'));
console.log('pilotMessage:', !!document.getElementById('pilotMessage'));
console.log('message1:', !!document.getElementById('message1'));
console.log('message2:', !!document.getElementById('message2'));
console.log('nextBtn:', !!document.getElementById('nextBtn'));
console.log('closeModalBtn:', !!document.getElementById('closeModalBtn'));

console.log('🔍 SCRIPT DE PRUEBA COMPLETADO');

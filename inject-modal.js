// Script para inyectar el modal dinámicamente en la página
console.log('🚀 Inyectando modal dinámicamente...');

// Función para crear e inyectar el modal
function injectModal() {
    // Verificar si ya existe
    if (document.getElementById('hcmModal')) {
        console.log('Modal ya existe');
        return;
    }
    
    // Crear el HTML del modal
    const modalHTML = `
        <div id="hcmModal" style="
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(0, 0, 0, 0.8) !important;
            z-index: 99999 !important;
            justify-content: center !important;
            align-items: center !important;
            opacity: 1 !important;
            visibility: visible !important;
        ">
            <div class="modal-content" style="
                background: linear-gradient(135deg, #2a3353 0%, #1e2746 100%) !important;
                border-radius: 12px !important;
                width: 90% !important;
                max-width: 500px !important;
                padding: 25px 20px !important;
                color: #fff !important;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
                border: 1px solid rgba(255, 255, 255, 0.1) !important;
            ">
                <div class="modal-message active" id="pilotMessage">
                    <h3 style="color: #fff; margin-top: 0;">🎯 Nueva Interfaz del Módulo Logístico</h3>
                    <p style="color: #fff; line-height: 1.6;">Estimado colaborador, nos complace informarte que ha sido liberada <strong>una nueva interfaz del módulo Logístico</strong>.</p>
                    
                    <h4 style="color: #fff; margin: 15px 0 10px 0;">🎯 Objetivos del Piloto:</h4>
                    <ul style="color: #fff; margin: 10px 0 0 20px;">
                        <li style="margin: 8px 0;">Evaluar la usabilidad de la nueva interfaz</li>
                        <li style="margin: 8px 0;">Identificar oportunidades de mejora</li>
                        <li style="margin: 8px 0;">Optimizar los procesos logísticos</li>
                        <li style="margin: 8px 0;">Recopilar feedback valioso para el desarrollo final</li>
                    </ul>
                    
                    <p style="color: #fff; line-height: 1.6;"><strong>Tu participación es fundamental</strong> para el éxito de este proyecto. Valoramos enormemente tu experiencia y esperamos contar con tus comentarios, sugerencias y observaciones.</p>
                    
                    <a href="mod_logistica.php" style="
                        display: inline-block;
                        background: linear-gradient(135deg, #00e1fd 0%, #0099cc 100%);
                        color: white;
                        text-decoration: none;
                        padding: 12px 20px;
                        border-radius: 6px;
                        margin: 15px 0;
                        font-weight: 600;
                    ">🔗 Acceder al Nuevo Módulo Logístico</a>
                    
                    <p style="margin-top: 15px; font-size: 0.9rem; color: rgba(255, 255, 255, 0.8);">
                        <strong>Nota:</strong> Puedes acceder al nuevo módulo en cualquier momento desde el botón de logística en el menú inferior.
                    </p>
                </div>
                
                <div class="modal-navigation" style="text-align: center; margin-top: 20px;">
                    <button id="nextBtn" style="
                        background: linear-gradient(135deg, #00e1fd 0%, #0099cc 100%);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 600;
                    ">Entendido</button>
                </div>
            </div>
        </div>
    `;
    
    // Inyectar el modal en el body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Configurar eventos
    const modal = document.getElementById('hcmModal');
    const nextBtn = document.getElementById('nextBtn');
    
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            console.log('Cerrando modal');
            modal.style.display = 'none';
        });
    }
    
    // Cerrar con Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal && modal.style.display !== 'none') {
            modal.style.display = 'none';
        }
    });
    
    // Cerrar al hacer clic fuera
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    console.log('✅ Modal inyectado y configurado correctamente');
}

// Ejecutar cuando el DOM esté listo
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', injectModal);
} else {
    injectModal();
}

// También ejecutar inmediatamente por si acaso
setTimeout(injectModal, 100);

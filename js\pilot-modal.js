/**
 * Script para gestionar el modal de mensajes múltiples
 * Maneja la navegación entre mensajes de mantenimiento, piloto y documentos pendientes
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Inicializando sistema de modal múltiple...');

    // Obtener el modal
    const modal = document.getElementById('hcmModal');

    if (!modal) {
        console.error('❌ Modal no encontrado en el DOM');
        return;
    }

    console.log('✅ Modal encontrado');

    // Variables de control
    let currentStep = 1;
    let totalSteps = 1;

    // Obtener todos los mensajes disponibles
    const maintenanceMessage = document.getElementById('maintenanceMessage');
    const pilotMessage = document.getElementById('pilotMessage');
    const message1 = document.getElementById('message1');
    const message2 = document.getElementById('message2');

    // Obtener elementos de navegación
    const nextBtn = document.getElementById('nextBtn');
    const closeBtn = document.getElementById('closeModalBtn');
    const progressText = document.getElementById('progressText');
    const dots = document.querySelectorAll('.modal-progress-dot');

    // Determinar la secuencia de mensajes y total de pasos
    const messageSequence = [];

    if (maintenanceMessage) {
        messageSequence.push('maintenanceMessage');
        console.log('📝 Mensaje de mantenimiento detectado');
    }

    if (pilotMessage) {
        messageSequence.push('pilotMessage');
        console.log('🎯 Mensaje del piloto detectado');
    }

    if (message1) {
        messageSequence.push('message1');
        console.log('📢 Mensaje 1 (documentos) detectado');
    }

    if (message2) {
        messageSequence.push('message2');
        console.log('📋 Mensaje 2 (HCM) detectado');
    }

    totalSteps = messageSequence.length;
    console.log(`📊 Total de pasos: ${totalSteps}`);
    console.log(`🔄 Secuencia: ${messageSequence.join(' → ')}`);

    // Función para mostrar un mensaje específico
    function showMessage(step) {
        console.log(`🔄 Mostrando paso ${step} de ${totalSteps}`);

        // Ocultar todos los mensajes
        const allMessages = [maintenanceMessage, pilotMessage, message1, message2];
        allMessages.forEach(msg => {
            if (msg) {
                msg.classList.remove('active');
                msg.style.display = 'none';
            }
        });

        // Mostrar el mensaje correspondiente al paso actual
        const currentMessageId = messageSequence[step - 1];
        const currentMessage = document.getElementById(currentMessageId);

        if (currentMessage) {
            currentMessage.classList.add('active');
            currentMessage.style.display = 'block';
            console.log(`✅ Mensaje ${currentMessageId} mostrado`);
        }

        // Actualizar indicadores de progreso
        updateProgress(step);

        // Actualizar botón
        updateButton(step);

        // Mostrar/ocultar botón de cierre
        if (closeBtn) {
            closeBtn.style.display = (step === totalSteps) ? 'block' : 'none';
        }
    }

    // Función para actualizar el progreso visual
    function updateProgress(step) {
        // Actualizar dots
        dots.forEach((dot, index) => {
            if (index < step) {
                dot.classList.add('active');
            } else {
                dot.classList.remove('active');
            }
        });

        // Actualizar texto de progreso
        if (progressText) {
            if (totalSteps === 1) {
                progressText.textContent = 'Información del Módulo Logístico';
            } else {
                progressText.textContent = `Paso ${step} de ${totalSteps}`;
            }
        }
    }

    // Función para actualizar el botón
    function updateButton(step) {
        if (nextBtn) {
            if (step === totalSteps) {
                nextBtn.textContent = 'Entendido';
            } else {
                nextBtn.textContent = 'Siguiente';
            }
        }
    }

    // Función para avanzar al siguiente paso
    function nextStep() {
        if (currentStep < totalSteps) {
            currentStep++;
            showMessage(currentStep);
        } else {
            closeModal();
        }
    }

    // Función para cerrar el modal
    function closeModal() {
        console.log('🔒 Cerrando modal');
        modal.style.display = 'none';
        modal.style.opacity = '0';
        modal.style.visibility = 'hidden';
        modal.classList.remove('show');
    }

    // Event listeners
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            console.log('🖱️ Botón siguiente/entendido clickeado');
            nextStep();
        });
    }

    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            console.log('🖱️ Botón cerrar clickeado');
            closeModal();
        });
    }

    // Cerrar modal al hacer clic fuera
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            console.log('🖱️ Clic fuera del modal');
            closeModal();
        }
    });

    // Cerrar modal con tecla Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display !== 'none') {
            console.log('⌨️ Tecla Escape presionada');
            closeModal();
        }
    });

    // Inicializar el modal
    console.log('🎬 Iniciando modal...');

    // Forzar la visualización del modal
    modal.style.display = 'flex';
    modal.style.opacity = '1';
    modal.style.visibility = 'visible';
    modal.classList.add('show');

    // Asegurar que el contenido del modal también esté visible
    const modalContent = modal.querySelector('.modal-content');
    if (modalContent) {
        modalContent.style.display = 'block';
        modalContent.style.opacity = '1';
        modalContent.style.visibility = 'visible';
    }

    // Mostrar el primer mensaje
    showMessage(1);

    console.log('✅ Sistema de modal múltiple inicializado correctamente');
});